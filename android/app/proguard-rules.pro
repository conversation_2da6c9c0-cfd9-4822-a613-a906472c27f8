# Essential Flutter classes
-keep class io.flutter.app.** { *; }
-keep class io.flutter.embedding.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.plugins.** { *; }

# Firebase essentials
-keep class com.google.firebase.** { *; }
-dontwarn com.google.firebase.**

# Awesome Notifications - only essential classes
-keep class me.carda.awesome_notifications.core.broadcasters.receivers.RefreshSchedulesReceiver { *; }
-keep class me.carda.awesome_notifications.core.broadcasters.receivers.ScheduledNotificationReceiver { *; }
-keepclassmembers class me.carda.awesome_notifications.core.broadcasters.receivers.** {
    public <init>();
    public <init>(android.content.Context);
}

# AudioService essentials
-keep class com.ryanheise.audioservice.AudioService { *; }
-keep class com.ryanheise.audioservice.MediaButtonReceiver { *; }

# App-specific classes (only main package)
-keep class com.perfectfit.qurankareem.MainActivity { *; }
-keep class com.perfectfit.qurankareem.AndroidAzanWidget** { *; }

# Suppress common warnings
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**
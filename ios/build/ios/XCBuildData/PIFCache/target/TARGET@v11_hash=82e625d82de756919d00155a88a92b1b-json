{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c5e94ab85f07fb5f0c76a6669cc1b5c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64 i386", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) AUDIO_SESSION_MICROPHONE=0", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b5842d6cd3b3c7ea9494980201b4a49", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d44f6f75317e6fd6d283a929a3449ba8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64 i386", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) AUDIO_SESSION_MICROPHONE=0", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985fc7bc5b4c6063ba2dcd0385c413bc88", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d44f6f75317e6fd6d283a929a3449ba8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64 i386", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) AUDIO_SESSION_MICROPHONE=0", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98556bc5e9a3ec856218c34a9ebb4b50ad", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9853a02dff78e49ec19139bf36ae0f5772", "guid": "bfdfe7dc352907fc980b868725387e986664320c746b7e8b6f1a81e39dedfe49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b79e294be2dbf10339d2b97e31d2826", "guid": "bfdfe7dc352907fc980b868725387e98c39648e88e4d0aa751697295b2444408"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985df91199625a79e5321f5b2c3ddbca15", "guid": "bfdfe7dc352907fc980b868725387e98fe233865df3587b792f600ed4d006daa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f529f94c4968bb524b9ef0239f057177", "guid": "bfdfe7dc352907fc980b868725387e98e6c0cc69e206412dee60f5578217248e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9abd6f203d3c9deb58cf2e5b368fc1e", "guid": "bfdfe7dc352907fc980b868725387e986cc01dfe97c283fac7b39fd229e2d8d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc253828b3f46e8e0f815eb13017424d", "guid": "bfdfe7dc352907fc980b868725387e98cac31d7a418cae6bcd95c7968872469b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898f0fce812cd76b14bc59a03296c375e", "guid": "bfdfe7dc352907fc980b868725387e987fb0fdaca3bdb3b95377b7992f738727", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc4dafe37ab392bfdd55c1c63b8686f1", "guid": "bfdfe7dc352907fc980b868725387e98b04f6f0cb21338ec7538e8d56bba66fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc0198d174c0474140a13e3035d458e1", "guid": "bfdfe7dc352907fc980b868725387e98b6ba26b4f7ee882e73e839f1916dbe31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980afe072d1c6a535da24a43d5298f1919", "guid": "bfdfe7dc352907fc980b868725387e985aeea3d25c8d9c7e05ef285f47f478e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a535c140dfd30ba75215cbdaae617eac", "guid": "bfdfe7dc352907fc980b868725387e98041b14e57c4c679f93182291f1cd0e8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98021fcf5b6bbf221f4742da432f9bd6be", "guid": "bfdfe7dc352907fc980b868725387e98ed246fda0ee492751b9f0e5e17d4758c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bb1cd160e0596c6fb7eb68e55a435ae", "guid": "bfdfe7dc352907fc980b868725387e98e77bf2ee6c9d81fcf42c36cfedbcc0e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4112cabf41de93c8ea615f6b97128f0", "guid": "bfdfe7dc352907fc980b868725387e988b77d6a307440bc580921f47d08eb291"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5a02de71c9fa1fe060795c2171ccddb", "guid": "bfdfe7dc352907fc980b868725387e981477236d73dabdda4f053f5115fa6ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc907d7bd13d7d33b3448d134995771e", "guid": "bfdfe7dc352907fc980b868725387e98c9846a16f6a72edb3edaf5bb9e4f57cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b8c2b4fddf455fd1f06022be105bd27", "guid": "bfdfe7dc352907fc980b868725387e981123d65a79408c4b27901f6fbf76ca84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d93538fbc9f0cb72a21870334252e27", "guid": "bfdfe7dc352907fc980b868725387e98393f473451411a4d0f26d842fbb08979"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a0ec667a56f846b4b13754e5bc3fd1f", "guid": "bfdfe7dc352907fc980b868725387e9859b53f1748052b3633f046e3cbb82c18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a2e0f92ae7ebb3adc34b642b7512270", "guid": "bfdfe7dc352907fc980b868725387e9864a31d137df90f5e60c9d4bdfa4a83ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bc0225a38daaf5d75ce3852be210c70", "guid": "bfdfe7dc352907fc980b868725387e983ae732200afd64cc3cfa8f4a0a955e47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838e971a3c74962a4d0837f51fd04af40", "guid": "bfdfe7dc352907fc980b868725387e9820b6483ba2baea5ade8d4d764f5bbf68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828cac3d642d05c1d449d2ef15c5daf94", "guid": "bfdfe7dc352907fc980b868725387e987495fc3176c5e00239679f00568817c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884298c388e78636cc810d6a1d6285b2b", "guid": "bfdfe7dc352907fc980b868725387e98d75373ab011e951be59e259e29279667"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8de352721155257afb5c2f103d81f56", "guid": "bfdfe7dc352907fc980b868725387e9843a2cea4e151b976a79491f5a0ace57a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d8fd08eea0c70b4a83f1871fe968ed", "guid": "bfdfe7dc352907fc980b868725387e980af9215d57b8e5395bd1d1ecd29e3ec8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a72f2da4687365a459429980bc0f89", "guid": "bfdfe7dc352907fc980b868725387e98ad725e1f12cebdc2a971293a50442f46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989283506c9adad1689c71d94e208ec558", "guid": "bfdfe7dc352907fc980b868725387e9812c1fe589ead02a79e1817ff4b42810c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56fa5ca71a555d1d4afcc390643b404", "guid": "bfdfe7dc352907fc980b868725387e98e18844e0682f076b746ec5de854901f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a1bd65d05fa1df303142cc43c3c7d2a", "guid": "bfdfe7dc352907fc980b868725387e98bdf17fa13c78fd0c32971d60bb144c29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893deb8d3a0e6605b6e3323d02e895e81", "guid": "bfdfe7dc352907fc980b868725387e98f0830e7bb66f987b8079df30bd9588cc"}], "guid": "bfdfe7dc352907fc980b868725387e9878c0c0ffd1eaa7179492fdf895c05b2e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9887f743c4f1beebee8e0af13258f30896", "guid": "bfdfe7dc352907fc980b868725387e984d3ee22d343679bedfc8181b4bac8d2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c3704f7bb30129437bc3e96ed783758", "guid": "bfdfe7dc352907fc980b868725387e9849cc93f9a83608499055e541d9272f0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dca5b722719a26ad93b8fc0244cc2838", "guid": "bfdfe7dc352907fc980b868725387e9830630fc8681c024ff85c6d4777407a13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b16807585e6bfa326e78cd00785cb0c", "guid": "bfdfe7dc352907fc980b868725387e98b8a935cb2a8d060454fcda27e7be3853"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7333efa35fda315fdb18b8d8cf8d090", "guid": "bfdfe7dc352907fc980b868725387e9824a6a02186e1a5ba56b84b7da6b01a40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98318cf508e826ecd06cf92197d68235f1", "guid": "bfdfe7dc352907fc980b868725387e989faab9d18eea6ae87d61d253076ecb18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821a2390034123a1f343518a4b0481bda", "guid": "bfdfe7dc352907fc980b868725387e987d71486f303d578704b1973f982ee7cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af7efe6a07b39a9b6498caee6df7fb96", "guid": "bfdfe7dc352907fc980b868725387e98bc93ab286adfccb82400077792d93773"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821577c04788d7636794ac031445c197b", "guid": "bfdfe7dc352907fc980b868725387e985f22c9ef1f34d925a3584e8fc5842b74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988514bd9f423581ec8044c0dbbb8dbdcf", "guid": "bfdfe7dc352907fc980b868725387e98b3b00446d526dfd9c11388fec22c83d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ea3fdade212b07334a3820f6f5b9467", "guid": "bfdfe7dc352907fc980b868725387e984b5617e668c0b003bd87b5787963cad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e27ebc98022c6b52eb04b7ef4f7c04b", "guid": "bfdfe7dc352907fc980b868725387e981a74ac069ae136375eb2197b5ae2f149"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f32e26773c46829dcd0da2515e55ef31", "guid": "bfdfe7dc352907fc980b868725387e98a7199247669cd328897bea85efc405af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982129b202c228850a161d92bb02075be5", "guid": "bfdfe7dc352907fc980b868725387e98fc5a1129c531c8b9f4cfd49fd571ac21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d3082ed597951693adcfaf35819be0", "guid": "bfdfe7dc352907fc980b868725387e980f83ff8e756f7646ca438855289b1003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98849df7d6b31b1d3dd64376fa3016057c", "guid": "bfdfe7dc352907fc980b868725387e9869aa069825a355053592b6cdedd5d027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823f5c6077a0f58d7af7a4394c34ddfc1", "guid": "bfdfe7dc352907fc980b868725387e9865b4a0c48ec4a31db8cc528a240ca829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf10d8faefec8201bc7b5eda05cea919", "guid": "bfdfe7dc352907fc980b868725387e987a65f31c031e2b3b1c810f40bedaad94"}], "guid": "bfdfe7dc352907fc980b868725387e98bf69cd66fd46d327d158932979b6fdfd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986cfaa8876ee8c1a3edc59113350e86bb", "guid": "bfdfe7dc352907fc980b868725387e989c1a369028dc12581bb51432218d4a71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5603aaa779fc608172a71fd010661dc", "guid": "bfdfe7dc352907fc980b868725387e987fc62ba5b5e92c3d5fd562d6a7ae499b"}], "guid": "bfdfe7dc352907fc980b868725387e987d5316db75171b16a60a5111545ec17c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ac0ace39b39888ef1af6df9155101b40", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98631a9e1cbb4571eb84d9732d9f68c9c9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
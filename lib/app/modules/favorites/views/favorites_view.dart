import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/favorites/controllers/favorites_controller.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/widget/provider_widget.dart';

class FavoritesView extends GetView<FavoriteController> {
  const FavoritesView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          Get.find<LanguageController>().keys.value.favorites!,
        ),
        // "Favorites"
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background_bottom.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        alignment: Alignment.center,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.topCenter,
              child: Obx(() {
                if (controller.favoriteProviders.isNotEmpty) {
                  return ListView.builder(
                    itemCount: controller.favoriteProviders.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final provider = controller.favoriteProviders[index];

                      return Padding(
                        padding: const EdgeInsets.all(6.0),
                        child: ProviderWidget(
                          provider: Provider.fromJson(provider.toJson()),
                          showBooking: true,
                          // isHour: controller.choosedPopularService.value.service
                          //         ?.pricingOptionId ==
                          //     1,
                          // isMeter: controller.choosedPopularService.value
                          //         .service?.pricingOptionId ==
                          //     2,
                          onTap: () =>
                              Get.toNamed(Routes.PROVIDER_PAGE, arguments: {
                            'provider': provider,
                            'service': ProviderServices.fromJson(
                                provider.services!.firstOrNull?.toJson() ?? {}
                                // controller
                                // .choosedPopularService.value.service!
                                // .toJson()
                                ),
                          }),
                          // serviceId: controller
                          //     .choosedPopularService.value.service?.id,
                        ),
                      );
                    },
                  );
                } else {
                  return const SizedBox.shrink();
                }
              }),
            ),
            Obx(() {
              if (controller.loading.value) {
                return const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: LinearProgressIndicator(
                    backgroundColor: Colors.transparent,
                    color: primaryColor,
                  ),
                );
              } else {
                return const SizedBox.shrink();
              }
            }),
          ],
        ),
      ),
    );
  }
}

// class FavoritesView extends GetView<FavoriteController> {
//   const FavoritesView({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     final favorites = controller.favorite.value.favorites ?? <FavoritesModel>[];
//
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(
//           // Get
//           // .find<LanguageController>()
//           // .keys
//           // .value
//           // .favorite!
//             "Favorites"
//           /
//         ),
//         centerTitle: true,
//       ),
//       body: Container(
//         decoration: const BoxDecoration(
//           image: DecorationImage(
//             image: AssetImage(
//               'assets/images/main_background_bottom.png',
//             ),
//             fit: BoxFit.fill,
//           ),
//         ),
//         alignment: Alignment.center,
//         child:,
//       ),
//     );
//   }
// }

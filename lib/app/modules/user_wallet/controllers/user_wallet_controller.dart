import 'package:get/get.dart';
import 'package:get_clean/app/modules/user_wallet/controllers/states/user_wallet_state.dart';

import '../../../../global/help_functions/help_functions.dart';
import '../../../../global/models/user_billings.dart';
import '../provider/user_wallet_remote_provider.dart';

class UserWalletController extends GetxController {
  final provider = UserWalletRemoteProvider();
  final state = UserWalletState().obs;
  final walletModel = UserBilling().obs;
  final choosedIndex = 0.obs;
  final deposit = 0.0.obs;
  final totalAmount = 0.0.obs;
  final totalBalance = 0.0.obs;

  List<BillingData> get deposits => walletModel.value.data!
      .where((e) => e.type!.contains('deposit'))
      .toList();

  List<BillingData> get totalAmounts => walletModel.value.data!
      .where((e) => e.type!.contains('total_amount'))
      .toList();

  @override
  void onInit() {
    super.onInit();
    getWallet();
  }

  void getWallet() async {
    state.value = UserWalletLoading();
    update();

    state.value = await provider.getWallet();

    if (state.value is UserWalletSuccess) {
      walletModel.value = state.value.wallet!;
      deposit.value = calculateDepositsTotal(walletModel.value);
      totalAmount.value = calculateTotalAmount(walletModel.value);
      totalBalance.value = calculateTotalBalance(walletModel.value);
      update();
    } else if (state.value is UserWalletFailed) {
      showErrorToast(state.value.errorMessage!);
    }
    update();
  }

  void onTapBarChange(index) {
    choosedIndex.value = index;
    update();
  }

  double calculateDepositsTotal(UserBilling billing) {
    double total = 0.0;
    for (var e in billing.data!) {
      if (e.type!.contains("deposit")) {
        total = total + e.amount!;
      }
    }
    return total;
  }

  double calculateTotalAmount(UserBilling billing) {
    double total = 0.0;
    for (var e in billing.data!) {
      if (e.type!.contains("total_amount")) {
        total = total + e.amount!;
      }
    }
    return total;
  }

  double calculateTotalBalance(UserBilling billing) {
    double total = 0.0;
    for (var e in billing.data!) {
      total = total + e.amount!;
    }
    return total;
  }
}

import 'package:get_clean/app/modules/user_wallet/controllers/states/user_wallet_state.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/models/user_billings.dart';

class UserWalletRemoteProvider {
  DioHelper helper = DioHelper();

  Future<UserWalletState> getWallet() async {
    try {
      final response = await helper.getData(userWalletURL);
      if (response['success'] == true) {
        return UserWalletSuccess(UserBilling.fromJson(response));
      } else {
        return UserWalletFailed(response['message']);
      }
    } catch (e) {
      return UserWalletFailed(e.toString());
    }
  }
}

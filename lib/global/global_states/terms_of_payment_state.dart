import '../models/terms_of_payment.dart';

class TermsOfPaymentState {
  TermsOfPaymentModel? termsOfPayment;
  String? errorMessage;
}

class TermsOfPaymentSuccessState extends TermsOfPaymentState {
  TermsOfPaymentSuccessState(TermsOfPaymentModel termsOfPayment) {
    this.termsOfPayment = termsOfPayment;
  }
}

class TermsOfPaymentErrorState extends TermsOfPaymentState {
  TermsOfPaymentErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

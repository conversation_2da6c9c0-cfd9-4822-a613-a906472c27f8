import 'dart:developer';

import 'package:get_clean/global/models/provider_services.dart';

class WalletModel {
  int? code;
  bool? success;
  WalletData? data;

  WalletModel({this.code, this.success, this.data});

  WalletModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'] != null ? WalletData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class WalletData {
  num? totalBalance;
  Deposits? deposits;
  Deposits? done;
  Deposits? tips;

  WalletData({
    this.totalBalance,
    this.deposits,
    this.done,
    this.tips,
  });

  WalletData.fromJson(Map<String, dynamic> json) {
    totalBalance = json['total_balance'];
    deposits =
        json['deposits'] != null ? Deposits.fromJson(json['deposits']) : null;
    done = json['done'] != null ? Deposits.fromJson(json['done']) : null;
    tips = json['tips'] != null ? Deposits.fromJson(json['tips']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_balance'] = totalBalance;
    if (deposits != null) {
      data['deposits'] = deposits!.toJson();
    }
    if (done != null) {
      data['done'] = done!.toJson();
    }

    if (tips != null) {
      data['tips'] = tips!.toJson();
    }
    return data;
  }
}

class Deposits {
  List<Transactions>? transactions;
  num? totalAmount;

  Deposits({this.transactions, this.totalAmount});

  Deposits.fromJson(Map<String, dynamic> json) {
    if (json['transactions'] != null) {
      transactions = <Transactions>[];
      json['transactions'].forEach((v) {
        transactions!.add(Transactions.fromJson(v));
      });
    }
    totalAmount = json['total_amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (transactions != null) {
      data['transactions'] = transactions!.map((v) => v.toJson()).toList();
    }
    data['total_amount'] = totalAmount;
    return data;
  }
}

class Transactions {
  int? id;
  String? type;
  String? description;
  num? amount;
  String? date;

  Transactions({this.id, this.type, this.description, this.amount, this.date});

  Transactions.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    type = json['type'];
    description = json['description'];
    amount = json['amount'];
    date = json['date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['type'] = type;
    data['description'] = description;
    data['amount'] = amount;
    data['date'] = date;
    return data;
  }
}

//{
//     "code": 200,
//     "success": true,
//     "data": {
//         "done": [
//             {
//                 "id": 162,
//                 "user": "Test User",
//                 "order_data": {
//                     "type": "service",
//                     "data": {
//                         "id": 1,
//                         "name": "Service 01",
//                         "image": "https://vishvish-clean.com/demo/public/storage/services/944fKCweGIsV72WRtC57xDH1o1mh6ne03BEhfj5m.png",
//                         "tracking": false,
//                         "pricing_option": {
//                             "id": 1,
//                             "name": "Hours",
//                             "has_types": false,
//                             "option_types": null
//                         },
//                         "types_settings": null
//                     }
//                 },
//                 "date": [
//                     "2023-09-24"
//                 ],
//                 "day": [
//                     "sunday"
//                 ],
//                 "status": "completed",
//                 "total_price": 376.2,
//                 "sub_total": 300,
//                 "provider_total_price": 300,
//                 "unit_provider_price": 100,
//                 "need_materials": false,
//                 "material_price": 0,
//                 "commission": 30,
//                 "tips": 0
//             }
//         ],
//         "completed": [
//             {
//                 "id": 161,
//                 "user": "Test User",
//                 "order_data": {
//                     "type": "service",
//                     "data": {
//                         "id": 1,
//                         "name": "Service 01",
//                         "image": "https://vishvish-clean.com/demo/public/storage/services/944fKCweGIsV72WRtC57xDH1o1mh6ne03BEhfj5m.png",
//                         "tracking": false,
//                         "pricing_option": {
//                             "id": 1,
//                             "name": "Hours",
//                             "has_types": false,
//                             "option_types": null
//                         },
//                         "types_settings": null
//                     }
//                 },
//                 "date": [
//                     "2023-09-24"
//                 ],
//                 "day": [
//                     "sunday"
//                 ],
//                 "status": "completed",
//                 "total_price": 501.6,
//                 "sub_total": 400,
//                 "provider_total_price": 400,
//                 "unit_provider_price": 100,
//                 "need_materials": false,
//                 "material_price": 0,
//                 "commission": 40,
//                 "tips": 0
//             },
//             {
//                 "id": 160,
//                 "user": "Test User",
//                 "order_data": {
//                     "type": "service",
//                     "data": {
//                         "id": 1,
//                         "name": "Service 01",
//                         "image": "https://vishvish-clean.com/demo/public/storage/services/944fKCweGIsV72WRtC57xDH1o1mh6ne03BEhfj5m.png",
//                         "tracking": false,
//                         "pricing_option": {
//                             "id": 1,
//                             "name": "Hours",
//                             "has_types": false,
//                             "option_types": null
//                         },
//                         "types_settings": null
//                     }
//                 },
//                 "date": [
//                     "2023-09-25"
//                 ],
//                 "day": [
//                     "monday"
//                 ],
//                 "status": "completed",
//                 "total_price": 125.4,
//                 "sub_total": 100,
//                 "provider_total_price": 100,
//                 "unit_provider_price": 50,
//                 "need_materials": true,
//                 "material_price": 50,
//                 "commission": 10,
//                 "tips": 0
//             }
//         ]
//     }
// }

class DoneAndCompletedOrderModel {
  final List<AllOrderModel>? done;
  final List<AllOrderModel>? notPaid;

  DoneAndCompletedOrderModel({
    this.done,
    this.notPaid,
  });

  factory DoneAndCompletedOrderModel.fromJson(Map<String, dynamic> json) {
    log('Dataaa ${json}');

    return DoneAndCompletedOrderModel(
      done: json['done'] != null
          ? (json['done'] as List)
              .map((i) => AllOrderModel.fromJson(i))
              .toList()
          : [],
      notPaid: json['completed'] != null
          ? (json['completed'] as List)
              .map((i) => AllOrderModel.fromJson(i))
              .toList()
          : [],
    );
  }
}

class AllOrderModel {
  final int? id;
  final String? user;
  final OrderData? orderData;
  final List<String>? date;
  final String? day;
  final String? status;
  final num? totalPrice;
  final num? subTotal;
  final num? providerTotalPrice;
  final num? unitProviderPrice;
  final bool? needMaterials;
  final num? materialPrice;
  final num? commission;
  final num? tips;

  AllOrderModel({
    this.id,
    this.user,
    this.orderData,
    this.date,
    this.day,
    this.status,
    this.totalPrice,
    this.subTotal,
    this.providerTotalPrice,
    this.unitProviderPrice,
    this.needMaterials,
    this.materialPrice,
    this.commission,
    this.tips,
  });

  factory AllOrderModel.fromJson(Map<String, dynamic> json) {
    log('Day: ${json['day']}');
    return AllOrderModel(
      id: json['id'],
      user: json['user'],
      orderData: OrderData.fromJson(json['order_data']),
      date: json['date'] != null ? List<String>.from(json['date']) : [],
      day: json['day'],
      status: json['status'],
      totalPrice: json['total_price'],
      subTotal: json['sub_total'],
      providerTotalPrice: json['provider_total_price'],
      unitProviderPrice: json['unit_provider_price'],
      needMaterials: json['need_materials'],
      materialPrice: json['material_price'],
      commission: json['commission'],
      tips: json['tips'],
    );
  }
}

class OrderData {
  final String? type;
  final OrderDataModel? data;

  OrderData({
    this.type,
    this.data,
  });

  factory OrderData.fromJson(Map<String, dynamic> json) {
    return OrderData(
      type: json['type'],
      data: OrderDataModel.fromJson(json['data']),
    );
  }
}

class OrderDataModel {
  final int? id;
  final String? name;
  final String? image;
  final bool? tracking;
  final PricingOption? pricingOption;
  final dynamic typesSettings;

  OrderDataModel({
    required this.id,
    required this.name,
    required this.image,
    required this.tracking,
    required this.pricingOption,
    required this.typesSettings,
  });

  factory OrderDataModel.fromJson(Map<String, dynamic> json) {
    return OrderDataModel(
      id: json['id'],
      name: json['name'],
      image: json['image'],
      tracking: json['tracking'],
      pricingOption: PricingOption.fromJson(json['pricing_option']),
      typesSettings: json['types_settings'],
    );
  }
}

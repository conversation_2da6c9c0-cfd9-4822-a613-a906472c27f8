// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SerializerOrder _$SerializerOrderFromJson(Map<String, dynamic> json) {
  // final productWithQuantityList = json['products_quantity'] as List<dynamic>?;
  //
  // final productWithQuantity = productWithQuantityList?.map((e) {
  //   final product = e['product'] as Map<String, dynamic>;
  //   final quantity = e['quantity'] as int;
  //   return (SerializerProduct.fromJson(product), quantity);
  // }).toList();

  final productWithQuantityList = json['products_quantity'] ?? [];

  final productWithQuantity = List<ProductQuantityModel>.from(
    productWithQuantityList.map(
      (product) => ProductQuantityModel.fromJson(product),
    ),
  );

  return SerializerOrder(
      id: json['id'] as int?,
      createdAt: json['createdAt'] as String?,
      total: (json['total'] as num?)?.toDouble(),
      user: json['user'] == null
          ? null
          : SerializerUser.fromJson(json['user'] as Map<String, dynamic>),
      shipping: json['shipping'] == null
          ? null
          : SerializerShipping.fromJson(
              json['shipping'] as Map<String, dynamic>),
      payment: json['payment'] == null
          ? null
          : SerializerPayment.fromJson(json['payment'] as Map<String, dynamic>),
      products: productWithQuantity

      // (json['products'] as List<dynamic>?)
      //     ?.map((e) => SerializerProduct.fromJson(e as Map<String, dynamic>))
      //     .toList(),
      );
}

Map<String, dynamic> _$SerializerOrderToJson(SerializerOrder instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdAt': instance.createdAt,
      'total': instance.total,
      'user': instance.user?.toJson(),
      'shipping': instance.shipping?.toJson(),
      'payment': instance.payment?.toJson(),
      // 'products': instance.products?.map((e) => e.toJson()).toList(),
    };

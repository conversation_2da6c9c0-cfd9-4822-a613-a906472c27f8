import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:eraser/eraser.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:xr_helper/xr_helper.dart';

class AndroidAwesomeNotifyService {
  static int createUniqueId(String prayerName, DateTime dateTime) {
    final id = prayerName.hashCode ^ dateTime.hashCode;
    return id.abs();
  }

  static Future<bool> createScheduleNotification({
    required int id,
    required DateTime dateTime,
    bool isReminder = false,
    String? title,
    String? body,
    String? sound,
  }) async {
    // if (kDebugMode) return true;

    final isAllowed = await AwesomeNotifications().isNotificationAllowed();

    final isShrouk = title == "وقت الشروق";

    final channelKey = isShrouk
        ? AppConsts.shroukChannelKey
        : isReminder
            ? AppConsts.reminderChannelKey
            : AppConsts.azanChannelKey;

    final timeZone = await AwesomeNotifications().getLocalTimeZoneIdentifier();

    if (isAllowed) {
      Log.w('Creating_Schedule_Notification: $id\n'
          'Title: $title\n'
          'Body: $body\n'
          'DateTime: ${dateTime.formatDateToStringWithTime}\n'
          'ChannelKey: $channelKey\n'
          // 'Sound: $sound\n'
          'TimeZone: $timeZone');
      return await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: channelKey,
          criticalAlert: true,
          category: NotificationCategory.Status,
          title: title,
          body: body,
          payload: {
            'scheduledTime': dateTime.toIso8601String(),
          },
        ),
        schedule: NotificationCalendar(
          day: dateTime.day,
          month: dateTime.month,
          year: dateTime.year,
          hour: dateTime.hour,
          minute: dateTime.minute,
          second: 0,
          allowWhileIdle: true,
          repeats: false,
          timeZone: timeZone,
        ),
      );
    } else {
      await AwesomeNotifications().requestPermissionToSendNotifications();
    }

    return isAllowed;
  }

  static Future<void> cancelScheduledNotificationById(int id) async {
    await AwesomeNotifications().cancel(id);
  }

  static Future<void> cancelAllScheduledNotifications() async {
    await AwesomeNotifications().cancelAll();
    await AwesomeNotifications().cancelAllSchedules();
    Log.w('Cancelled all awesome notifications');
  }

  //listScheduledNotifications
  static Future<List<NotificationModel>> listScheduledNotifications() async {
    return await AwesomeNotifications().listScheduledNotifications();
  }

  static Future<void>
      clearAppNotificationsIfNoScheduledAzanOrReminders() async {
    final now = DateTime.now();
    const bufferDuration = Duration(minutes: 3);

    // Retrieve all scheduled notifications
    final scheduledNotifications =
        await AndroidAwesomeNotifyService.listScheduledNotifications();

    // Check if any scheduled notification is within the 3-minute window
    final hasUpcomingNotifications = scheduledNotifications.any((notification) {
      final scheduledTime = DateTime.tryParse(
          notification.content?.payload?['scheduledTime'] ?? '');

      if (scheduledTime == null) return false;

      return scheduledTime.isAfter(now.subtract(bufferDuration)) &&
          scheduledTime.isBefore(now.add(bufferDuration));
    });

    // Clear notifications only if no upcoming Azan or reminders
    if (!hasUpcomingNotifications) {
      Eraser.clearAllAppNotifications();
      // Log.w('Cleared all app notifications as no Azan or reminders are scheduled.');
    } else {
      // Log.w('Skipped clearing notifications due to upcoming Azan or reminders.');
    }
  }
}

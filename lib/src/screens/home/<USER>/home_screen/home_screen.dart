import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/current_activity.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/attendees_of_today.dart';
import 'widgets/home_grid_view/home_grid_view.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.symmetric(vertical: AppSpaces.mediumPadding),
      shrinkWrap: true,
      children: [
        const AttendeesOfToday(),
        context.mediumGap,
        if (const UserModel().isAdmin) const CurrentActivity(),
        context.mediumGap,
        const HomeGridView(),
      ],
    ).paddingSymmetric(horizontal: AppSpaces.mediumPadding);
  }
}

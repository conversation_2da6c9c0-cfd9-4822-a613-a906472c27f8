<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the Completer constructor from the Class Completer class from the dart:async library, for the Dart programming language.">
  <title>Completer constructor - Completer class - dart:async library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
    <li><a href="dart-async/Completer-class.html">Completer<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
    <li class="self-crumb">Completer factory constructor</li>
  </ol>
  <div class="self-name">Completer</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-async/dart-async-library.html">dart:async</a></li>
      <li><a href="dart-async/Completer-class.html">Completer<span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></a></li>
      <li class="self-crumb">Completer factory constructor</li>
    </ol>
    
    <h5>Completer class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-async/Completer-class.html#constructors">Constructors</a></li>
      <li><a href="dart-async/Completer/Completer.html">Completer</a></li>
      <li><a href="dart-async/Completer/Completer.sync.html">sync</a></li>
    
      <li class="section-title">
        <a href="dart-async/Completer-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-async/Completer/future.html">future</a></li>
      <li><a href="dart-async/Completer/isCompleted.html">isCompleted</a></li>
      <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-async/Completer-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-async/Completer/complete.html">complete</a></li>
      <li><a href="dart-async/Completer/completeError.html">completeError</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
      <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
    
      <li class="section-title inherited"><a href="dart-async/Completer-class.html#operators">Operators</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">Completer&lt;<wbr><span class="type-parameter">T</span>&gt;</span> constructor</h1></div>

    <section class="multi-line-signature">
      
      <span class="name ">Completer&lt;<wbr><span class="type-parameter">T</span>&gt;</span>(<wbr>)
    </section>

    <section class="desc markdown">
      <p>Creates a new completer.</p>
<p>The general workflow for creating a new future is to 1) create a
new completer, 2) hand out its future, and, at a later point, 3) invoke
either <a href="dart-async/Completer/complete.html">complete</a> or <a href="dart-async/Completer/completeError.html">completeError</a>.</p>
<p>The completer completes the future asynchronously. That means that
callbacks registered on the future are not called immediately when
<a href="dart-async/Completer/complete.html">complete</a> or <a href="dart-async/Completer/completeError.html">completeError</a> is called. Instead the callbacks are
delayed until a later microtask.</p>
<p>Example:</p>
<pre class="language-dart"><code class="language-dart">var completer = new Completer();
handOut(completer.future);
later: {
  completer.complete('completion value');
}
</code></pre>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">factory Completer() =&gt; new _AsyncCompleter&lt;T&gt;();</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>

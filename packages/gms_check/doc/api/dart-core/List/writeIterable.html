<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the writeIterable method from the List class, for the Dart programming language.">
  <title>writeIterable method - List class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/List-class.html">List<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
    <li class="self-crumb">writeIterable&lt;<wbr><span class="type-parameter">T</span>&gt; method</li>
  </ol>
  <div class="self-name">writeIterable</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/List-class.html">List<span class="signature">&lt;<wbr><span class="type-parameter">E</span>&gt;</span></a></li>
      <li class="self-crumb">writeIterable&lt;<wbr><span class="type-parameter">T</span>&gt; method</li>
    </ol>
    
    <h5>List class</h5>
    <ol>
    
        <li class="section-title"><a href="dart-core/List-class.html#constructors">Constructors</a></li>
        <li><a class="deprecated" href="dart-core/List/List.html">List</a></li>
        <li><a href="dart-core/List/List.empty.html">empty</a></li>
        <li><a href="dart-core/List/List.filled.html">filled</a></li>
        <li><a href="dart-core/List/List.from.html">from</a></li>
        <li><a href="dart-core/List/List.generate.html">generate</a></li>
        <li><a href="dart-core/List/List.of.html">of</a></li>
        <li><a href="dart-core/List/List.unmodifiable.html">unmodifiable</a></li>
    
        <li class="section-title">
            <a href="dart-core/List-class.html#instance-properties">Properties</a>
        </li>
        <li><a href="dart-core/List/first.html">first</a></li>
        <li><a href="dart-core/List/last.html">last</a></li>
        <li><a href="dart-core/List/length.html">length</a></li>
        <li><a href="dart-core/List/reversed.html">reversed</a></li>
        <li class="inherited"><a href="dart-core/Object/hashCode.html">hashCode</a></li>
        <li class="inherited"><a href="dart-core/Iterable/isEmpty.html">isEmpty</a></li>
        <li class="inherited"><a href="dart-core/Iterable/isNotEmpty.html">isNotEmpty</a></li>
        <li class="inherited"><a href="dart-core/Iterable/iterator.html">iterator</a></li>
        <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
        <li class="inherited"><a href="dart-core/Iterable/single.html">single</a></li>
    
        <li class="section-title"><a href="dart-core/List-class.html#instance-methods">Methods</a></li>
        <li><a href="dart-core/List/add.html">add</a></li>
        <li><a href="dart-core/List/addAll.html">addAll</a></li>
        <li><a href="dart-core/List/asMap.html">asMap</a></li>
        <li><a href="dart-core/List/cast.html">cast</a></li>
        <li><a href="dart-core/List/clear.html">clear</a></li>
        <li><a href="dart-core/List/fillRange.html">fillRange</a></li>
        <li><a href="dart-core/List/getRange.html">getRange</a></li>
        <li><a href="dart-core/List/indexOf.html">indexOf</a></li>
        <li><a href="dart-core/List/indexWhere.html">indexWhere</a></li>
        <li><a href="dart-core/List/insert.html">insert</a></li>
        <li><a href="dart-core/List/insertAll.html">insertAll</a></li>
        <li><a href="dart-core/List/lastIndexOf.html">lastIndexOf</a></li>
        <li><a href="dart-core/List/lastIndexWhere.html">lastIndexWhere</a></li>
        <li><a href="dart-core/List/remove.html">remove</a></li>
        <li><a href="dart-core/List/removeAt.html">removeAt</a></li>
        <li><a href="dart-core/List/removeLast.html">removeLast</a></li>
        <li><a href="dart-core/List/removeRange.html">removeRange</a></li>
        <li><a href="dart-core/List/removeWhere.html">removeWhere</a></li>
        <li><a href="dart-core/List/replaceRange.html">replaceRange</a></li>
        <li><a href="dart-core/List/retainWhere.html">retainWhere</a></li>
        <li><a href="dart-core/List/setAll.html">setAll</a></li>
        <li><a href="dart-core/List/setRange.html">setRange</a></li>
        <li><a href="dart-core/List/shuffle.html">shuffle</a></li>
        <li><a href="dart-core/List/sort.html">sort</a></li>
        <li><a href="dart-core/List/sublist.html">sublist</a></li>
        <li class="inherited"><a href="dart-core/Iterable/any.html">any</a></li>
        <li class="inherited"><a href="dart-core/Iterable/contains.html">contains</a></li>
        <li class="inherited"><a href="dart-core/Iterable/elementAt.html">elementAt</a></li>
        <li class="inherited"><a href="dart-core/Iterable/every.html">every</a></li>
        <li class="inherited"><a href="dart-core/Iterable/expand.html">expand</a></li>
        <li class="inherited"><a href="dart-core/Iterable/firstWhere.html">firstWhere</a></li>
        <li class="inherited"><a href="dart-core/Iterable/fold.html">fold</a></li>
        <li class="inherited"><a href="dart-core/Iterable/followedBy.html">followedBy</a></li>
        <li class="inherited"><a href="dart-core/Iterable/forEach.html">forEach</a></li>
        <li class="inherited"><a href="dart-core/Iterable/join.html">join</a></li>
        <li class="inherited"><a href="dart-core/Iterable/lastWhere.html">lastWhere</a></li>
        <li class="inherited"><a href="dart-core/Iterable/map.html">map</a></li>
        <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
        <li class="inherited"><a href="dart-core/Iterable/reduce.html">reduce</a></li>
        <li class="inherited"><a href="dart-core/Iterable/singleWhere.html">singleWhere</a></li>
        <li class="inherited"><a href="dart-core/Iterable/skip.html">skip</a></li>
        <li class="inherited"><a href="dart-core/Iterable/skipWhile.html">skipWhile</a></li>
        <li class="inherited"><a href="dart-core/Iterable/take.html">take</a></li>
        <li class="inherited"><a href="dart-core/Iterable/takeWhile.html">takeWhile</a></li>
        <li class="inherited"><a href="dart-core/Iterable/toList.html">toList</a></li>
        <li class="inherited"><a href="dart-core/Iterable/toSet.html">toSet</a></li>
        <li class="inherited"><a href="dart-core/Object/toString.html">toString</a></li>
        <li class="inherited"><a href="dart-core/Iterable/where.html">where</a></li>
        <li class="inherited"><a href="dart-core/Iterable/whereType.html">whereType</a></li>
    
        <li class="section-title"><a href="dart-core/List-class.html#operators">Operators</a></li>
        <li><a href="dart-core/List/operator_plus.html">operator +</a></li>
        <li><a href="dart-core/List/operator_equals.html">operator ==</a></li>
        <li><a href="dart-core/List/operator_get.html">operator []</a></li>
        <li><a href="dart-core/List/operator_put.html">operator []=</a></li>
    
    
        <li class="section-title"><a href="dart-core/List-class.html#static-methods">Static methods</a></li>
        <li><a href="dart-core/List/castFrom.html">castFrom</a></li>
        <li><a href="dart-core/List/copyRange.html">copyRange</a></li>
        <li><a href="dart-core/List/writeIterable.html">writeIterable</a></li>
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-method">writeIterable&lt;<wbr><span class="type-parameter">T</span>&gt;</span> method</h1></div>

    <section class="multi-line-signature">
      <span class="returntype">void</span>
            <span class="name ">writeIterable</span>
&lt;<wbr><span class="type-parameter">T</span>&gt;(<wbr><span class="parameter" id="writeIterable-param-target"><span class="type-annotation"><a href="dart-core/List-class.html">List</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">target</span>, </span> <span class="parameter" id="writeIterable-param-at"><span class="type-annotation"><a href="dart-core/int-class.html">int</a></span> <span class="parameter-name">at</span>, </span> <span class="parameter" id="writeIterable-param-source"><span class="type-annotation"><a href="dart-core/Iterable-class.html">Iterable</a><span class="signature">&lt;<wbr><span class="type-parameter">T</span>&gt;</span></span> <span class="parameter-name">source</span></span>)
      
    </section>
    <section class="desc markdown">
      <p>Write the elements of an iterable into a list.</p>
<p>This is a utility function that can be used to implement methods like
<a href="dart-core/List/setAll.html">setAll</a>.</p>
<p>The elements of <code>source</code> are written into <code>target</code> from position <code>at</code>.
The <code>source</code> must not contain more elements after writing the last
position of <code>target</code>.</p>
<p>If the source is a list, the <a href="dart-core/List/copyRange.html">copyRange</a> function is likely to be more
efficient.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">static void writeIterable&lt;T&gt;(List&lt;T&gt; target, int at, Iterable&lt;T&gt; source) {
  RangeError.checkValueInInterval(at, 0, target.length, &quot;at&quot;);
  int index = at;
  int targetLength = target.length;
  for (var element in source) {
    if (index == targetLength) {
      throw IndexError(targetLength, target);
    }
    target[index] = element;
    index++;
  }
}</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>

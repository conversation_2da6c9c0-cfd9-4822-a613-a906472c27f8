name: barber_app
description: "Barber Application"
publish_to: 'none'

version: 1.0.0+1

scripts:
  build_runner: dart run build_runner build --delete-conflicting-outputs
  launch_icons: flutter pub run flutter_launcher_icons:main
  launch_splash: flutter pub run flutter_native_splash:create

environment:
  sdk: ^3.6.1


dependencies:
  flutter:
    sdk: flutter

  #? Localization
  flutter_localizations:
    sdk: flutter

  # * Helper Package *
  xr_helper:
    path: packages/xr_helper

  cupertino_icons: ^1.0.6

  #? State Management
  flutter_riverpod: ^2.4.9
  riverpod: ^2.4.9
  hooks_riverpod: ^2.4.9
  flutter_hooks: ^0.20.4
  equatable:

  #? responsive
  flutter_screenutil: ^5.9.3


  #? Google Fonts
  google_fonts:

  #? Assets
  lottie: ^3.0.0
  flutter_svg:
  smooth_page_indicator: ^1.2.0+3

  #? form Builder
  flutter_form_builder: ^9.4.1
  form_builder_image_picker: ^4.1.0
  form_builder_validators: ^11.0.0

  #? Calendar View
  calendar_timeline:
    path: packages/calendar_timeline

  #? Utils
  fluttertoast: ^8.2.8
  permission_handler: ^11.3.1
  shimmer_animation: ^2.2.2
  intl: ^0.20.2
  quickalert: ^1.1.0

  #? Location
  google_maps_flutter: ^2.11.0
  geolocator: ^13.0.1

  #? Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.4.2

  #? UI
  loading_animation_widget: ^1.3.0
  carousel_slider: ^5.0.0
  font_awesome_flutter: ^10.7.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_otp_text_field: ^1.4.0+2
  slide_rating_dialog:
    path: packages/slide_rating_dialog
  webview_flutter: ^4.13.0
  http:

dependency_overrides:
  archive: ^3.6.1
  win32: ^5.5.4
  flutter_plugin_android_lifecycle: ^2.0.27
  intl: ^0.20.2


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.9
  build_runner:
  flutter_gen_runner:

#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/logo.png"
#  adaptive_icon_background: "assets/images/app_icon.png"
#  adaptive_icon_foreground: "assets/images/app_icon.png"
#  adaptive_icon_foreground_inset: 16

# ? dart run flutter_native_splash:create
flutter_native_splash:
  android: true
  ios: true
  web: false
  fullscreen: false
  color: '#ffffff'
  image: 'assets/images/logo.png'
  android_12:
    color: '#ffffff'
    image: 'assets/images/logo.png'

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
#    - assets/animated/

flutter_intl:
  enabled: true


flutter_gen:
  output: lib/generated
{"kind": "collectionType", "collectionName": "configs", "info": {"singularName": "config", "pluralName": "configs", "displayName": "Configs", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"banner_limit": {"type": "integer", "default": 3}, "sizes": {"type": "component", "repeatable": true, "component": "extra-settings.extra-settings"}, "colors": {"type": "component", "repeatable": true, "component": "extra-settings.extra-settings"}, "vendor": {"type": "relation", "relation": "oneToOne", "target": "api::vendor.vendor", "inversedBy": "config"}, "currencies": {"type": "relation", "relation": "manyToMany", "target": "api::currency.currency", "mappedBy": "configs"}, "countries": {"type": "relation", "relation": "oneToMany", "target": "api::country.country", "mappedBy": "config"}, "show_pricing": {"type": "boolean", "default": true}, "show_map": {"type": "boolean", "default": false}, "show_boundaries": {"type": "boolean", "default": false}, "minimum_order_cost": {"type": "integer"}, "is_active_working_time": {"type": "boolean", "default": true}, "order_ringing_bell": {"type": "boolean", "default": false}, "primary_color": {"type": "string"}, "default_language": {"type": "enumeration", "enum": ["en", "ar"], "default": "en"}}}